#!/usr/bin/env python3
"""
Test script to verify the new centering improvements work correctly.
"""

import sys
from pathlib import Path
import numpy as np

# Add src to path for imports
current_dir = Path(__file__).parent
src_path = current_dir / "src"
if src_path.exists():
    sys.path.insert(0, str(src_path))

def test_enhanced_centering():
    """Test the enhanced face/chest/hip centering method."""
    print("Testing enhanced centering with hip keypoints...")
    
    try:
        from src.photo_center.utils.config import Config
        from src.photo_center.image_processing.centering import PhotoCenterer
        
        # Create config with new hip weight
        config = Config()
        
        # Test that hip_weight property works
        hip_weight = config.hip_weight
        print(f"✓ Hip weight loaded from config: {hip_weight}")
        
        # Create test image
        test_image = np.zeros((600, 800, 3), dtype=np.uint8)
        test_image[200:400, 300:500] = [128, 128, 128]  # Gray rectangle
        
        # Create mock detection with face, chest, and hip keypoints
        mock_detection = {
            'bbox': [300, 200, 500, 400],
            'confidence': 0.85,
            'center': (400, 300),
            'keypoints': {
                # Face keypoints
                'nose': (400, 220),
                'left_eye': (390, 210),
                'right_eye': (410, 210),
                'left_ear': (380, 215),
                'right_ear': (420, 215),
                # Chest keypoints
                'left_shoulder': (370, 260),
                'right_shoulder': (430, 260),
                # Hip keypoints
                'left_hip': (380, 350),
                'right_hip': (420, 350)
            }
        }
        
        # Test centering
        centerer = PhotoCenterer(config)
        result = centerer.center_subject(test_image, mock_detection)
        
        print(f"✓ Enhanced centering works: confidence={result.confidence:.3f}, method={result.method_used}")
        print(f"  Subject center: {result.subject_center}")
        print(f"  Target center: {result.target_center}")
        
        return True
        
    except Exception as e:
        print(f"✗ Enhanced centering error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_centering_methods():
    """Test all available centering methods."""
    print("\nTesting all centering methods...")
    
    try:
        from src.photo_center.utils.config import Config
        from src.photo_center.image_processing.centering import PhotoCenterer
        
        # Create test image
        test_image = np.zeros((600, 800, 3), dtype=np.uint8)
        test_image[200:400, 300:500] = [128, 128, 128]
        
        # Mock detection
        mock_detection = {
            'bbox': [300, 200, 500, 400],
            'confidence': 0.85,
            'center': (400, 300),
            'keypoints': {
                'nose': (400, 220),
                'left_eye': (390, 210),
                'right_eye': (410, 210),
                'left_shoulder': (370, 260),
                'right_shoulder': (430, 260),
                'left_hip': (380, 350),
                'right_hip': (420, 350)
            }
        }
        
        methods = ['face_chest_based', 'keypoint_based', 'bbox_based', 'center_of_mass']
        
        for method in methods:
            config = Config()
            config.set('centering.method', method)
            
            centerer = PhotoCenterer(config)
            result = centerer.center_subject(test_image, mock_detection)
            
            print(f"✓ Method '{method}': confidence={result.confidence:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Centering methods error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_weight_configuration():
    """Test weight configuration and normalization."""
    print("\nTesting weight configuration...")
    
    try:
        from src.photo_center.utils.config import Config
        from src.photo_center.image_processing.centering import PhotoCenterer
        
        # Test different weight configurations
        weight_configs = [
            {'face': 0.5, 'chest': 0.3, 'hip': 0.2},  # Default
            {'face': 0.7, 'chest': 0.2, 'hip': 0.1},  # Face-heavy
            {'face': 0.2, 'chest': 0.4, 'hip': 0.4},  # Body-heavy
            {'face': 1.0, 'chest': 1.0, 'hip': 1.0},  # Equal (should normalize)
        ]
        
        test_image = np.zeros((600, 800, 3), dtype=np.uint8)
        mock_detection = {
            'bbox': [300, 200, 500, 400],
            'confidence': 0.85,
            'keypoints': {
                'nose': (400, 220),
                'left_shoulder': (370, 260),
                'right_shoulder': (430, 260),
                'left_hip': (380, 350),
                'right_hip': (420, 350)
            }
        }
        
        for i, weights in enumerate(weight_configs):
            config = Config()
            config.set('centering.method', 'face_chest_based')
            config.set('centering.face_weight', weights['face'])
            config.set('centering.chest_weight', weights['chest'])
            config.set('centering.hip_weight', weights['hip'])
            
            centerer = PhotoCenterer(config)
            result = centerer.center_subject(test_image, mock_detection)
            
            print(f"✓ Weight config {i+1}: F={weights['face']}, C={weights['chest']}, H={weights['hip']} -> confidence={result.confidence:.3f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Weight configuration error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all centering improvement tests."""
    print("Photo Center Centering Improvements Test")
    print("=" * 50)
    
    tests = [
        test_enhanced_centering,
        test_centering_methods,
        test_weight_configuration,
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"\n✗ Unexpected error in {test.__name__}: {e}")
            results.append(False)
    
    print("\n" + "=" * 50)
    print("Test Summary:")
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✓ All {total} tests passed!")
        print("\nCentering improvements are working correctly!")
        return 0
    else:
        print(f"✗ {total - passed} out of {total} tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
